using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Plugins.OpenApi;
using System.Net.Http;
using Mysoft.GPTEngine.Domain.ApiAuthorization;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement
{
    public class ApiToolsImporter
    {
        private readonly Kernel _kernel;
        private readonly ILogger<ApiToolsImporter> _logger;
        private readonly PluginRepostory _pluginRepository;
        private readonly PluginMetadataRepostory _pluginMetadataRepository;
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        private readonly IConfigurationService _configurationService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly MysoftConfigurationDomain _mysoftConfigurationDomain;
        private readonly ChatRunDto _chatRunDto;

        public ApiToolsImporter(Kernel kernel, IServiceProvider serviceProvider, ChatRunDto chatRunDto)
        {
            _kernel = kernel;
            _logger = serviceProvider.GetService<ILoggerFactory>().CreateLogger<ApiToolsImporter>();
            _pluginRepository = serviceProvider.GetService<PluginRepostory>();
            _pluginMetadataRepository = serviceProvider.GetService<PluginMetadataRepostory>();
            _mysoftMemoryCache = serviceProvider.GetService<MysoftMemoryCache>();
            _configurationService = serviceProvider.GetService<IConfigurationService>();
            _httpContextAccessor = serviceProvider.GetService<IHttpContextAccessor>();
            _mysoftContextFactory = serviceProvider.GetService<IMysoftContextFactory>();
            _mysoftConfigurationDomain = serviceProvider.GetService<MysoftConfigurationDomain>();
            _chatRunDto = chatRunDto;
        }

        public async Task ImportApiTools(ConcurrentDictionary<string, string> toolTitleMapping = null)
        {
            var agent = _chatRunDto.Agent;
            if (agent?.Tools == null || !agent.Tools.Any())
            {
                _logger.LogInformation("[ImportApiTools] 没有API工具需要导入");
                return;
            }

            var groupedTools = agent.Tools.GroupBy(tool => tool.PluginGUID);
            foreach (var group in groupedTools)
            {
                var pluginGuid = group.Key;
                var tools = group.ToList();
                var paths = tools.Select(tool => tool.Path).ToList();
                
                try
                {
                    _logger.LogInformation("[ImportApiTools] 尝试注册插件 PluginGUID: {pluginGuid}, Paths: {@paths}", 
                        pluginGuid, paths);
                    
                    var pluginEntity = await _pluginRepository.GetAsync(f => f.PluginGUID == pluginGuid);
                    var pluginMetadataEntity = await _pluginMetadataRepository.GetAsync(f => f.PluginGUID == pluginGuid);
                    
                    if (pluginEntity == null || pluginMetadataEntity == null)
                    {
                        _logger.LogWarning("[ImportApiTools] 未找到插件或元数据，PluginGUID: {pluginGuid}", pluginGuid);
                        continue;
                    }

                    _logger.LogInformation("[ImportApiTools] 获取到插件实体和元数据，PluginCode: {pluginCode}", 
                        pluginEntity.PluginCode);
                    
                    var authorization = ApiAuthorizationFactory.GetAuthorization(pluginEntity.AuthMode,
                        _kernel, _mysoftMemoryCache, _configurationService, _httpContextAccessor);
                    
                    var authenticate = new OpenApiServiceHelper.Authenticate(authorization.AuthenticateRequestAsync);
                    
                    var plugin = await CreateKernelPlugin(pluginMetadataEntity, authenticate, pluginEntity, paths);

                    if (plugin != null)
                    {
                        _logger.LogInformation("[ImportApiTools] 插件注册成功，PluginCode: {pluginCode}, KernelPlugin: {plugin}",
                            pluginEntity.PluginCode, plugin);

                        // 添加工具标题映射，使用pluginEntity.PluginCode作为显示标题
                        if (toolTitleMapping != null)
                        {
                            foreach (var function in plugin)
                            {
                                string toolKey = $"{pluginEntity.PluginCode}.{function.Name}";
                                string displayTitle = pluginEntity.PluginCode;
                                toolTitleMapping.TryAdd(toolKey, displayTitle);
                                _logger.LogInformation("[ImportApiTools] 添加API工具标题映射: {toolKey} -> {title}", toolKey, displayTitle);
                            }
                        }
                    }
                    else
                    {
                        _logger.LogWarning("[ImportApiTools] 插件注册返回null，PluginCode: {pluginCode}",
                            pluginEntity.PluginCode);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[ImportApiTools] 注册插件异常，PluginGUID: {pluginGuid}, 错误信息: {msg}",
                        pluginGuid, ex.Message);
                }
            }
        }

        private async Task<KernelPlugin> CreateKernelPlugin(PluginMetadataEntity pluginMetadataEntity,
            OpenApiServiceHelper.Authenticate authenticate, PluginEntity pluginEntity, List<string> operationsToExclude)
        {
            _logger.LogInformation("[CreateKernelPlugin] 开始注册插件，PluginCode: {pluginCode}, PluginGUID: {pluginGuid}",
                pluginEntity.PluginCode, pluginEntity.PluginGUID);
            
            string docMetadata = pluginMetadataEntity.Metadata;
            byte[] byteArray = Encoding.UTF8.GetBytes(docMetadata);
            
            var apiRequestOptions = await OpenApiServiceHelper.GetAgentOpenApiRequestOptions(pluginEntity,
                pluginMetadataEntity, _mysoftContextFactory, _mysoftConfigurationDomain);

            _logger.LogInformation("[CreateKernelPlugin] 插件配置信息，PluginCode: {pluginCode}, Source: {source}, ServerUrlOverride: {serverUrl}",
                pluginEntity.PluginCode, pluginEntity.Source, apiRequestOptions.ServerUrlOverride?.ToString() ?? "null");
            
            var executionParameters = new OpenApiFunctionExecutionParameters(
                serverUrlOverride: apiRequestOptions.ServerUrlOverride,
                ignoreNonCompliantErrors: true,
                authCallback: async (request, cancellationToken) =>
                {
                    _logger.LogInformation("[CreateKernelPlugin] 执行认证回调，PluginCode: {pluginCode}",
                        pluginEntity.PluginCode);
                    await authenticate(request, apiRequestOptions, cancellationToken);
                }
            ) { HttpResponseContentReader = ReadHttpResponseContentAsync };

            _logger.LogInformation("[CreateKernelPlugin] ExecutionParameters ServerUrlOverride: {serverUrl}",
                executionParameters.ServerUrlOverride?.ToString() ?? "null");
            
            try
            {
                using (var stream = new MemoryStream(byteArray))
                {
                    stream.Position = 0;
                    _logger.LogInformation("[CreateKernelPlugin] 解析OpenAPI文档PluginCode: {pluginCode}",
                        pluginEntity.PluginCode);

                    var plugin = await _kernel.CreatePluginFromOpenApiAsync(pluginEntity.PluginCode, stream, executionParameters);

                    _logger.LogInformation(
                        "[CreateKernelPlugin] 插件导入成功，PluginCode: {pluginCode}, KernelPlugin: {plugin}",
                        pluginEntity.PluginCode, plugin);

                    return plugin;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CreateKernelPlugin] 注册插件异常，PluginCode: {pluginCode}, 错误信息: {msg}",
                    pluginEntity.PluginCode, ex.Message);
                throw;
            }
        }



        private static async Task<object?> ReadHttpResponseContentAsync(HttpResponseContentReaderContext context,
            CancellationToken cancellationToken)
        {
            if (context.Response.Content.Headers.ContentType?.MediaType == "text/event-stream")
            {
                return await context.Response.Content.ReadAsStreamAsync();
            }

            if (context.Request.Headers.Contains("x-stream"))
            {
                return await context.Response.Content.ReadAsStreamAsync();
            }

            return null;
        }
    }
}
